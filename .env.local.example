DEPLOYMENT_ENV=development
SESSION_TYPE=filesystem

# Entra ID Configuration for Authentication
# For testing purposes, we'll use development mode instead of Entra ID
# AZURE_CLIENT_ID is optional for user-assigned identities. Omit it for system-assigned.
AZURE_CLIENT_ID=92e443a6-5081-4e5c-aea4-7d8df64d050d
AZURE_TENANT_ID=ee78877a-c63a-405d-85d6-8914358aa533
AZURE_APP_SECRET=****************************************

# Frontend Environment Variables (prefixed with VITE_)
VITE_AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
VITE_AZURE_TENANT_ID=${AZURE_TENANT_ID}

# Authentication Configuration
# Set to "false" to use development mode authentication instead of Entra ID
USE_ENTRA_AUTH=true

# Development Mode
# Set to "true" to enable development mode features
DEVELOPMENT_MODE=false
# Set to "true" to allow client credentials fallback when delegated token is missing or invalid
ALLOW_CLIENT_CREDENTIALS_FALLBACK=false

# Authentication Configuration
# For production, set this to your app URL + /logout (e.g., https://your-app.azurewebsites.net/logout)
# For development, leave empty to use provider-specific logout URLs
AUTH_LOGOUT_ENDPOINT=
