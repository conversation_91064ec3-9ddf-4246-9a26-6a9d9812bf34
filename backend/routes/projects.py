from quart import jsonify, request, current_app
import logging
from datetime import datetime, timezone
import os
import json
import asyncio

# Import the deployment function
from deploy_project_resources import deploy_project_resources

# Import the Blueprint if it's not already imported
from quart import Blueprint

projects_bp = Blueprint("projects", __name__)


@projects_bp.route("/api/projects/<project_id>/deployment-status", methods=["GET"])
async def get_project_deployment_status(project_id):
    """Get the deployment status for a project."""
    try:
        # Check if we have a cosmos client
        if (
            not hasattr(current_app, "cosmos_conversation_client")
            or not current_app.cosmos_conversation_client
        ):
            logging.error("CosmosDB client not initialized")
            return jsonify(
                {
                    "status": "failed",
                    "updated_at": datetime.now(timezone.utc).isoformat(),
                    "details": {"error": "CosmosDB client not initialized"},
                }
            )

        # Get the project from CosmosDB
        if hasattr(current_app.cosmos_conversation_client, "get_project_by_id"):
            project = await current_app.cosmos_conversation_client.get_project_by_id(
                project_id
            )
        else:
            # Fall back to direct query
            parameters = [{"name": "@projectId", "value": project_id}]
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            projects = []
            async for (
                item
            ) in current_app.cosmos_conversation_client.container_client.query_items(
                query=query,
                parameters=parameters,
                partition_key=None,  # Query across all partition keys
            ):
                projects.append(item)

            project = projects[0] if projects else None

        if not project:
            logging.error(f"Project {project_id} not found")
            return jsonify(
                {
                    "status": "not_found",
                    "updated_at": datetime.now(timezone.utc).isoformat(),
                    "details": {"error": f"Project {project_id} not found"},
                }
            )

        # Check if the project has deployment information
        # First check for deployment_status field, then fall back to deployment field
        if "deployment_status" in project:
            deployment = project["deployment_status"]
            logging.info(f"Found deployment_status field for project {project_id}")
        elif "deployment" in project:
            deployment = project["deployment"]
            logging.info(f"Found deployment field for project {project_id}")
        else:
            # No deployment information, return default status
            logging.info(
                f"No deployment information found for project {project_id}, returning default status"
            )
            return jsonify(
                {
                    "status": "pending",
                    "updated_at": datetime.now(timezone.utc).isoformat(),
                    "details": {
                        "storage": {
                            "storage_account": False,
                            "containers": {
                                "uploads": False,
                                "input": False,
                                "output": False,
                            },
                        },
                        "storage_complete": False,
                        "search": {
                            "search_service": False,
                            "index": False,
                            "indexer": False,
                            "datasource": False,
                        },
                        "search_complete": False,
                        "function": {
                            "function_app": False,
                            "event_grid_topic": False,
                            "event_grid_system_topic": False,
                            "event_grid": False,
                            "maturity_assessment": False,
                            "executive_summary": False,
                        },
                        "function_complete": False,
                        "overall_complete": False,
                        "completion_percentage": 0,
                    },
                }
            )

        # Check if we have a deployment summary file
        deployment_summary_path = f"deployment_summary_{project_id}.json"
        if os.path.exists(deployment_summary_path):
            try:
                with open(deployment_summary_path, "r") as f:
                    deployment_summary = json.load(f)

                # Update the status with the deployment summary
                status = deployment_summary.get(
                    "status", deployment.get("status", "pending")
                )
                resources = deployment_summary.get("resources", {})

                # Create the details object
                details = {
                    "storage": {
                        "storage_account": bool(resources.get("storage_account_name")),
                        "containers": {
                            "uploads": bool(resources.get("uploads_container")),
                            "input": bool(resources.get("input_container")),
                            "output": bool(resources.get("output_container")),
                        },
                    },
                    "storage_complete": all(
                        [
                            bool(resources.get("storage_account_name")),
                            bool(resources.get("uploads_container")),
                            bool(resources.get("input_container")),
                            bool(resources.get("output_container")),
                        ]
                    ),
                    "search": {
                        "search_service": bool(resources.get("search_service_name")),
                        "index": bool(resources.get("search_index_name")),
                        "indexer": bool(resources.get("search_indexer_name")),
                        "datasource": bool(resources.get("search_datasource_name")),
                    },
                    "search_complete": all(
                        [
                            bool(resources.get("search_service_name")),
                            bool(resources.get("search_index_name")),
                            bool(resources.get("search_indexer_name")),
                            bool(resources.get("search_datasource_name")),
                        ]
                    ),
                    "function": {
                        "function_app": bool(resources.get("function_app_name")),
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": bool(
                            resources.get("azure_function_maturity_assessment_url")
                        ),
                        "executive_summary": bool(
                            resources.get("azure_function_executive_summary_url")
                        ),
                    },
                    "function_complete": bool(resources.get("function_app_name")),
                    "overall_complete": status == "completed",
                    "completion_percentage": (
                        100
                        if status == "completed"
                        else (
                            0
                            if status == "pending"
                            else (50 if status == "in_progress" else 0)
                        )
                    ),
                }

                return jsonify(
                    {
                        "status": status,
                        "updated_at": deployment_summary.get(
                            "timestamp", datetime.now(timezone.utc).isoformat()
                        ),
                        "details": details,
                    }
                )

            except Exception as e:
                logging.error(f"Error reading deployment summary file: {str(e)}")
                # Fall back to the deployment status from the project

        # Return the deployment status from the project
        return jsonify(
            {
                "status": deployment.get("status", "pending"),
                "updated_at": deployment.get(
                    "updated_at", datetime.now(timezone.utc).isoformat()
                ),
                "details": deployment.get("details", {}),
            }
        )
    except Exception as e:
        logging.error(f"Error checking deployment status: {str(e)}")
        return jsonify(
            {
                "status": "failed",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "details": {"error": str(e)},
            }
        )


@projects_bp.route("/api/projects/<project_id>/deployment-status", methods=["POST"])
async def update_project_deployment_status(project_id):
    """Update the deployment status for a project."""
    try:
        # Check if we have a cosmos client
        if (
            not hasattr(current_app, "cosmos_conversation_client")
            or not current_app.cosmos_conversation_client
        ):
            logging.error("CosmosDB client not initialized")
            return jsonify({"error": "CosmosDB client not initialized"}), 500

        # Get the project from CosmosDB
        if hasattr(current_app.cosmos_conversation_client, "get_project_by_id"):
            project = await current_app.cosmos_conversation_client.get_project_by_id(
                project_id
            )
        else:
            # Fall back to direct query
            parameters = [{"name": "@projectId", "value": project_id}]
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            projects = []
            async for (
                item
            ) in current_app.cosmos_conversation_client.container_client.query_items(
                query=query,
                parameters=parameters,
                partition_key=None,  # Query across all partition keys
            ):
                projects.append(item)

            project = projects[0] if projects else None

        if not project:
            logging.error(f"Project {project_id} not found")
            return jsonify({"error": f"Project {project_id} not found"}), 404

        # Get the request data
        data = await request.get_json()

        # Validate the data
        if not data or "status" not in data:
            return jsonify({"error": "Status is required"}), 400

        # Check if the project has 'deployment' or 'deployment_status' field
        if "deployment_status" in project:
            # Use deployment_status field
            deployment = project.get("deployment_status", {})

            # Update the deployment information
            deployment.update(data)
            deployment["updated_at"] = datetime.now(timezone.utc).isoformat()

            # Update the project
            project_data = {"deployment_status": deployment}
            logging.info(f"Updating project {project_id} with deployment_status field")
        else:
            # Use deployment field for backward compatibility
            deployment = project.get("deployment", {})

            # Update the deployment information
            deployment.update(data)
            deployment["updated_at"] = datetime.now(timezone.utc).isoformat()

            # Update the project
            project_data = {"deployment": deployment}
            logging.info(f"Updating project {project_id} with deployment field")

        # Get the region from the project
        region = project.get("region", "westeurope")

        # Save the project
        if hasattr(
            current_app.cosmos_conversation_client, "update_project"
        ) and callable(current_app.cosmos_conversation_client.update_project):
            # Check the signature of the update_project method
            import inspect

            sig = inspect.signature(
                current_app.cosmos_conversation_client.update_project
            )
            if "region" in sig.parameters:
                # If the method accepts a region parameter, use it
                await current_app.cosmos_conversation_client.update_project(
                    project_id=project_id, region=region, project_data=project_data
                )
            else:
                # Otherwise, use the method without the region parameter
                await current_app.cosmos_conversation_client.update_project(
                    project_id=project_id, project_data=project_data
                )
        else:
            # Fall back to direct update
            project.update(project_data)
            await current_app.cosmos_conversation_client.container_client.upsert_item(
                body=project
            )

        # Return the updated deployment status
        return jsonify(deployment), 200
    except Exception as e:
        logging.error(f"Error updating deployment status: {str(e)}")
        return jsonify({"error": str(e)}), 500


@projects_bp.route("/api/projects/<project_id>/deploy", methods=["POST"])
async def deploy_project(project_id):
    """Deploy resources for a project."""
    try:
        # Check if we have a cosmos client
        if (
            not hasattr(current_app, "cosmos_conversation_client")
            or not current_app.cosmos_conversation_client
        ):
            logging.error("CosmosDB client not initialized")
            return jsonify({"error": "CosmosDB client not initialized"}), 500

        # Get the project from CosmosDB
        if hasattr(current_app.cosmos_conversation_client, "get_project_by_id"):
            project = await current_app.cosmos_conversation_client.get_project_by_id(
                project_id
            )
        else:
            # Fall back to direct query
            parameters = [{"name": "@projectId", "value": project_id}]
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            projects = []
            async for (
                item
            ) in current_app.cosmos_conversation_client.container_client.query_items(
                query=query,
                parameters=parameters,
                partition_key=None,  # Query across all partition keys
            ):
                projects.append(item)

            project = projects[0] if projects else None

        if not project:
            logging.error(f"Project {project_id} not found")
            return jsonify({"error": f"Project {project_id} not found"}), 404

        # Get the project name
        project_name = project.get("name", "Unknown Project")

        # Update the deployment status to in_progress
        deployment = project.get("deployment", {})
        deployment["status"] = "in_progress"
        deployment["updated_at"] = datetime.now(timezone.utc).isoformat()
        deployment["message"] = "Starting deployment..."

        # Update the project
        project_data = {"deployment": deployment}

        # Get the region from the project
        region = project.get("region", "westeurope")

        # Save the project
        if hasattr(
            current_app.cosmos_conversation_client, "update_project"
        ) and callable(current_app.cosmos_conversation_client.update_project):
            # Check the signature of the update_project method
            import inspect

            sig = inspect.signature(
                current_app.cosmos_conversation_client.update_project
            )
            if "region" in sig.parameters:
                # If the method accepts a region parameter, use it
                await current_app.cosmos_conversation_client.update_project(
                    project_id=project_id, region=region, project_data=project_data
                )
            else:
                # Otherwise, use the method without the region parameter
                await current_app.cosmos_conversation_client.update_project(
                    project_id=project_id, project_data=project_data
                )
        else:
            # Fall back to direct update
            project.update(project_data)
            await current_app.cosmos_conversation_client.container_client.upsert_item(
                body=project
            )

        # Start the deployment in a background task
        # Note: The deployment process now waits for completion and doesn't retry if it fails
        asyncio.create_task(
            deploy_project_resources(
                project_id=project_id,
                project_name=project_name,
                region_id=region,  # Use the region from the project
                api_url=f"http://localhost:{os.environ.get('API_PORT', '50505')}",
            )
        )

        # Return success
        return jsonify({"status": "in_progress", "message": "Deployment started"}), 202
    except Exception as e:
        logging.error(f"Error starting deployment: {str(e)}")
        return jsonify({"error": str(e)}), 500
