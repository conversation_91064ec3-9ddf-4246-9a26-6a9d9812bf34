import logging
import os
from typing import Dict, Any, List

from quart import current_app
from azure.identity.aio import DefaultAzureCredential
from azure.core.credentials import AzureKeyCredential
from azure.mgmt.web.aio import WebSiteManagementClient

try:
    from azure.mgmt.eventgrid.aio import EventGridManagementClient
except ImportError:  # pragma: no cover
    from azure.mgmt.eventgrid import EventGridManagementClient

from backend.settings import app_settings
from backend.aisearch.search_manager import delete_search_resources
from backend.utils.project_retrieval import get_project_with_fallbacks

logger = logging.getLogger(__name__)


async def cleanup_project_resources(
    project_id: str, user_id: str = "anonymous"
) -> Dict[str, Any]:
    """Delete Azure resources associated with a project.

    This helper retrieves the project document from Cosmos DB and removes all
    provisioned Azure resources (storage containers, search components,
    function app, and Event Grid topics).
    """
    cosmos_client = current_app.cosmos_conversation_client
    if not cosmos_client:
        raise RuntimeError("CosmosDB client not available")

    project = await get_project_with_fallbacks(
        cosmos_client, project_id, user_id, using_azure_cli=True
    )
    if not project:
        raise ValueError(f"Project {project_id} not found")

    storage_containers: List[str] = [
        project.get("storage_container_uploads"),
        project.get("storage_container_input"),
        project.get("storage_container_output"),
    ]
    search_resources = {
        "index": project.get("search_index_name"),
        "datasource": project.get("search_datasource_name"),
        "indexer": project.get("search_indexer_name"),
    }
    function_app_name = project.get("function_app_name")

    logger.info(f"[CLEANUP] Starting Azure cleanup for project {project_id}")

    mgmt_cred = DefaultAzureCredential()
    subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
    resource_group = os.getenv("AZURE_RESOURCE_GROUP")

    search_service_endpoint = (
        f"https://{app_settings.azure_search.search_service_name}.search.windows.net"
    )
    search_key = app_settings.azure_search.key
    search_cred = AzureKeyCredential(search_key) if search_key else None

    results = {
        "storage_containers": [],
        "search_resources": {"index": False, "datasource": False, "indexer": False},
        "function_app": False,
    }

    # Delete Azure Search resources
    if search_cred:
        try:
            res = await delete_search_resources(
                search_service_endpoint=search_service_endpoint,
                credential=search_cred,
                index_name=search_resources["index"],
                indexer_name=search_resources["indexer"],
                datasource_name=search_resources["datasource"],
            )
            results["search_resources"] = res
        except Exception as exc:  # pragma: no cover - network required
            logger.error(f"[CLEANUP] Search resource deletion failed: {exc}")
    else:  # pragma: no cover - configuration issue
        logger.warning(
            "[CLEANUP] No search credential available, skipping search deletion"
        )

    # Delete storage containers
    if current_app.storage_management_client and resource_group:
        for container in storage_containers:
            if container:
                try:
                    await current_app.storage_management_client.blob_containers.delete(
                        resource_group_name=resource_group,
                        account_name=app_settings.azure_storage.account_name,
                        container_name=container,
                    )
                    results["storage_containers"].append(container)
                except Exception as exc:  # pragma: no cover
                    logger.error(
                        f"[CLEANUP] Failed to delete container {container}: {exc}"
                    )
    else:  # pragma: no cover
        logger.warning("[CLEANUP] Storage management client or resource group missing")

    # Delete Function App and Event Grid topics
    if function_app_name and subscription_id and resource_group:
        try:
            web_client = WebSiteManagementClient(mgmt_cred, subscription_id)
            await web_client.web_apps.begin_delete(
                resource_group_name=resource_group, name=function_app_name
            )
            results["function_app"] = True

            event_client = EventGridManagementClient(
                credential=mgmt_cred, subscription_id=subscription_id
            )
            await web_client.close()
        except Exception as exc:  # pragma: no cover
            logger.error(f"[CLEANUP] Function app deletion failed: {exc}")
    else:  # pragma: no cover
        logger.warning("[CLEANUP] Missing function app name or Azure configuration")

    if hasattr(mgmt_cred, "close"):
        await mgmt_cred.close()

    logger.info(f"[CLEANUP] Azure cleanup complete for project {project_id}")
    return results
