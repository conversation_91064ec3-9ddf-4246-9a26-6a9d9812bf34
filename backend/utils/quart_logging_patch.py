"""
Module to patch Quart's logging configuration.
This module provides functions to disable Quart's access logging.
"""

import logging
import os
import sys
from typing import Optional, Dict, Any

def patch_quart_logging():
    """
    Patch Quart's logging configuration to disable access logs.
    This function should be called before creating the Quart app.
    """
    try:
        # Configure Quart's logging directly
        logging.getLogger("quart").setLevel(logging.WARNING)
        logging.getLogger("quart.serving").setLevel(logging.WARNING)
        logging.getLogger("quart.app").setLevel(logging.WARNING)
        
        # Configure Hypercorn's logging directly
        logging.getLogger("hypercorn").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.access").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.error").setLevel(logging.WARNING)
        
        # Log that we've disabled access logging
        logging.info("Quart access logging has been disabled")
        return True
    except Exception as e:
        logging.warning(f"Could not patch Quart logging: {e}")
        return False

def patch_hypercorn_logging():
    """
    Patch Hypercorn's logging configuration.
    This function should be called before creating the Quart app.
    """
    try:
        # Configure Hypercorn's logging directly
        logging.getLogger("hypercorn").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.access").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.error").setLevel(logging.WARNING)
        
        logging.info("Hypercorn logging has been configured")
        return True
    except Exception as e:
        logging.warning(f"Could not patch Hypercorn logging: {e}")
        return False
        return False
    except Exception as e:
        logging.error(f"Error patching Quart logging: {e}")
        return False

def patch_hypercorn_logging():
    """
    Patch Hypercorn's logging configuration to disable access logs.
    This function should be called before starting the Hypercorn server.
    """
    try:
        # Try to import Hypercorn's logging module
        import hypercorn.logging
        
        # Save the original access_log function
        original_access_log = hypercorn.logging.access_log
        
        # Define our patched access_log function
        def patched_access_log(*args, **kwargs):
            # Check if we should disable access logging
            if os.environ.get("QUART_ACCESS_LOGGING", "1") == "0":
                # Do nothing, effectively disabling access logging
                return
            
            # Otherwise, call the original function
            return original_access_log(*args, **kwargs)
        
        # Replace Hypercorn's access_log function with our patched version
        hypercorn.logging.access_log = patched_access_log
        
        return True
    except ImportError:
        logging.warning("Could not import hypercorn.logging, access logging patch not applied")
        return False
    except Exception as e:
        logging.error(f"Error patching Hypercorn logging: {e}")
        return False
