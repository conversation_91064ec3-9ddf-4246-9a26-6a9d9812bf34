#!/usr/bin/env python3
"""
Simple Azure Cost Management test script
Tests Azure Cost Management SDK directly without backend imports
"""

import os
import asyncio
from datetime import datetime, timedelta

# Load environment variables from .env file
def load_env_file():
    """Load environment variables from .env file"""
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Remove quotes if present
                    value = value.strip('"').strip("'")
                    os.environ[key] = value
        print("✅ Environment variables loaded from .env file")
    except FileNotFoundError:
        print("⚠️ .env file not found, using system environment variables")
    except Exception as e:
        print(f"⚠️ Error loading .env file: {e}")

# Load environment variables
load_env_file()

# Try to import Azure SDK directly
try:
    from azure.identity import DefaultAzureCredential
    from azure.mgmt.costmanagement import CostManagementClient
    from azure.mgmt.costmanagement.models import (
        QueryDefinition,
        QueryTimePeriod,
        QueryDataset,
        QueryAggregation,
        QueryGrouping,
        QueryFilter,
        QueryComparisonExpression
    )
    print("✅ Azure SDK imports successful")
except ImportError as e:
    print(f"❌ Azure SDK import failed: {e}")
    print("Please install: pip install azure-mgmt-costmanagement azure-identity")
    exit(1)

async def test_azure_cost_direct():
    """Test Azure Cost Management SDK directly"""

    # Configuration from environment variables
    subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
    resource_group = os.environ.get("AZURE_RESOURCE_GROUP")
    project_id = "35a7db33-b409-4873-b3bc-54e42931bd3d"  # Priority Plot testing project

    print("🔧 Direct Azure Cost Management Test")
    print("=" * 50)
    print(f"Subscription ID: {subscription_id[:8] + '...' if subscription_id else 'NOT SET'}")
    print(f"Resource Group: {resource_group or 'NOT SET'}")
    print(f"Target Project ID: {project_id}")
    print()

    # Check required environment variables
    if not subscription_id:
        print("❌ AZURE_SUBSCRIPTION_ID environment variable not set")
        return
    if not resource_group:
        print("❌ AZURE_RESOURCE_GROUP environment variable not set")
        return

    try:
        # Initialize Azure credentials and client
        print("🔧 Initializing Azure credentials...")
        credential = DefaultAzureCredential()

        print("🔧 Creating Cost Management client...")
        client = CostManagementClient(
            credential=credential,
            subscription_id=subscription_id
        )
        print("✅ Cost Management client created successfully")

        # Define scope (resource group level)
        scope = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}"
        print(f"🎯 Using scope: {scope}")

        # Calculate time period (last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        print(f"📅 Time period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        time_period = QueryTimePeriod(
            from_property=start_date,
            to=end_date
        )

        # Test 1: Query all costs in resource group
        print("\n" + "="*50)
        print("TEST 1: All costs in resource group")
        print("="*50)

        dataset_all = QueryDataset(
            granularity="None",
            aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
            grouping=[{"type": "Dimension", "name": "ResourceId"}]
        )

        query_all = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=dataset_all,
        )

        print("🔧 Executing query for all resources...")
        response_all = client.query.usage(scope=scope, parameters=query_all)

        print(f"📊 Response type: {type(response_all)}")
        if hasattr(response_all, 'rows'):
            rows_all = response_all.rows or []
            print(f"📊 Total resources with costs: {len(rows_all)}")

            if rows_all:
                print("\n📋 Sample resource costs (first 5):")
                for i, row in enumerate(rows_all[:5], 1):
                    print(f"  {i}. {row}")

                # Calculate total cost
                total_cost = 0
                for row in rows_all:
                    if len(row) >= 2:
                        try:
                            cost = float(row[1])
                            total_cost += cost
                        except (ValueError, TypeError):
                            pass
                print(f"\n💰 Total cost for all resources: ${total_cost:.2f}")
            else:
                print("⚠️ No cost data found for any resources")

        # Test 2: Query with project-id tag filter using correct SDK approach
        print("\n" + "="*50)
        print(f"TEST 2: Costs filtered by project-id = '{project_id}' (Correct SDK Method)")
        print("="*50)

        try:
            # Build the tag filter using proper SDK models
            print("🔧 Building tag filter using QueryFilter and QueryComparisonExpression...")

            tag_filter = QueryFilter(
                tags=QueryComparisonExpression(
                    name="project-id",
                    operator="In",
                    values=[project_id]
                )
            )

            print(f"✅ Tag filter created for project-id = '{project_id}'")

            # Create dataset with proper tag filter
            dataset_tagged = QueryDataset(
                granularity="None",
                aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
                grouping=[QueryGrouping(type="Dimension", name="ResourceId")],
                filter=tag_filter
            )

            query_tagged = QueryDefinition(
                type="ActualCost",
                timeframe="Custom",
                time_period=time_period,
                dataset=dataset_tagged,
            )

            print(f"🔧 Executing query with proper SDK tag filter...")
            response_tagged = client.query.usage(scope=scope, parameters=query_tagged)

            if hasattr(response_tagged, 'rows'):
                rows_tagged = response_tagged.rows or []
                print(f"📊 Resources with project-id '{project_id}': {len(rows_tagged)}")

                if rows_tagged:
                    print(f"\n📋 Project '{project_id}' resources:")
                    project_total = 0
                    for i, row in enumerate(rows_tagged, 1):
                        print(f"  {i}. {row}")
                        if len(row) >= 2:
                            try:
                                cost = float(row[0])  # Cost is typically first column
                                project_total += cost
                            except (ValueError, TypeError):
                                pass
                    print(f"\n💰 Total cost for project '{project_id}': €{project_total:.2f}")
                else:
                    print(f"⚠️ No resources found with project-id tag = '{project_id}'")
            else:
                print("❌ No response rows received")

        except Exception as e:
            print(f"❌ SDK tag filter failed: {e}")
            import traceback
            traceback.print_exc()

            # Fallback: Try grouping by tag without filter to see all available tags
            print(f"\n🔧 Fallback: Querying all project-id tags to see what's available...")
            try:
                fallback_dataset = QueryDataset(
                    granularity="None",
                    aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
                    grouping=[QueryGrouping(type="TagKey", name="project-id")]
                )

                fallback_query = QueryDefinition(
                    type="ActualCost",
                    timeframe="Custom",
                    time_period=time_period,
                    dataset=fallback_dataset,
                )

                fallback_response = client.query.usage(scope=scope, parameters=fallback_query)

                if hasattr(fallback_response, 'rows') and fallback_response.rows:
                    print(f"📊 Found {len(fallback_response.rows)} project-id tag values:")
                    for i, row in enumerate(fallback_response.rows, 1):
                        print(f"  {i}. {row}")
                else:
                    print("⚠️ No project-id tags found in any resources")

            except Exception as fallback_error:
                print(f"❌ Fallback query also failed: {fallback_error}")

        # Test 3: Query all project-id tags using proper SDK models
        print("\n" + "="*50)
        print("TEST 3: All project-id tags in resource group (Proper SDK)")
        print("="*50)

        try:
            dataset_tags = QueryDataset(
                granularity="None",
                aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
                grouping=[QueryGrouping(type="TagKey", name="project-id")]
            )

            query_tags = QueryDefinition(
                type="ActualCost",
                timeframe="Custom",
                time_period=time_period,
                dataset=dataset_tags,
            )

            print("🔧 Executing query to find all project-id tags...")
            response_tags = client.query.usage(scope=scope, parameters=query_tags)

            if hasattr(response_tags, 'rows'):
                rows_tags = response_tags.rows or []
                print(f"📊 Different project-id values found: {len(rows_tags)}")

                if rows_tags:
                    print("\n📋 All project-id tags and their costs:")
                    for i, row in enumerate(rows_tags, 1):
                        print(f"  {i}. {row}")

                    # Check if our target project is in the results
                    target_found = any(project_id in str(row) for row in rows_tags)
                    if target_found:
                        print(f"\n✅ Target project '{project_id}' found in available tags!")
                    else:
                        print(f"\n⚠️ Target project '{project_id}' not found in available tags")
                        print("Available project IDs:")
                        for row in rows_tags:
                            if len(row) > 0:
                                print(f"  - {row[0] if row[0] else 'No tag value'}")
                else:
                    print("⚠️ No project-id tags found in any resources")

        except Exception as e:
            print(f"❌ Tag query failed: {e}")
            import traceback
            traceback.print_exc()

        # Test 4: Raw response inspection
        print("\n" + "="*50)
        print("TEST 4: Response structure analysis")
        print("="*50)

        print("🔍 Response attributes:")
        if hasattr(response_all, '__dict__'):
            for attr, value in response_all.__dict__.items():
                if attr != 'rows':  # Don't print all rows again
                    print(f"  {attr}: {type(value)} = {value}")

        print("\n🎯 SUMMARY")
        print("="*50)
        print("✅ Azure Cost Management SDK is working")
        print("✅ Authentication successful")
        print("✅ Resource group access confirmed")
        print()
        print("Key findings:")
        if hasattr(response_all, 'rows') and response_all.rows:
            print(f"- Found {len(response_all.rows)} resources with cost data")
        if hasattr(response_tagged, 'rows') and response_tagged.rows:
            print(f"- Found {len(response_tagged.rows)} resources tagged with projectId '{project_id}'")
        else:
            print(f"- No resources found with projectId '{project_id}' tag")
        if hasattr(response_tags, 'rows') and response_tags.rows:
            print(f"- Found {len(response_tags.rows)} different projectId values in use")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_azure_cost_direct())
