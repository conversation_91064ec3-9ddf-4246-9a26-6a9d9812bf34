#!/usr/bin/env python3
"""
Test script for the deployment process with the DeploymentSummary class.
This script will test both the DeploymentSummary class and perform a real deployment.
"""

import asyncio
import json
import logging
import os
import sys
import uuid
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Import the deployment function
try:
    from deploy_project_resources import deploy_project_resources
    logging.info("Successfully imported deploy_project_resources")
except ImportError as e:
    logging.error(f"Failed to import deploy_project_resources: {e}")
    sys.exit(1)

# Import the deployment status module
try:
    from backend.deployments.deployment_status import (
        update_deployment_summary,
        add_deployment_resource,
        add_deployment_error,
        DeploymentSummary
    )
    # Flag to indicate if we're using the new deployment status module
    using_deployment_status_module = True
    logging.info("Successfully imported deployment status module")
except ImportError as e:
    logging.warning(f"backend.deployments.deployment_status module not found: {e}")
    update_deployment_summary = None
    add_deployment_resource = None
    add_deployment_error = None
    DeploymentSummary = None
    using_deployment_status_module = False

async def test_deployment_summary():
    """Test the DeploymentSummary class."""
    project_id = "test-project"
    api_url = "http://localhost:50508"

    if not using_deployment_status_module:
        logging.error("DeploymentSummary class not available. Test failed.")
        return False

    logging.info("Testing DeploymentSummary class...")

    # Create a deployment summary handler
    deployment_summary = DeploymentSummary(project_id, api_url)

    # Load the existing summary
    summary = deployment_summary.load_summary()
    logging.info(f"Loaded summary: {summary}")

    # Update the status
    deployment_summary.update_status("in_progress", "Deployment in progress")
    logging.info("Updated status to in_progress")

    # Add some resources
    deployment_summary.add_resource("storage_account_name", "sttest123")
    deployment_summary.add_resource("search_service_name", "srch-test123")
    deployment_summary.add_resource("function_app_name", "func-test123")
    logging.info("Added resources")

    # Add an error
    deployment_summary.add_error("Test error message")
    logging.info("Added error")

    # Update the status to completed
    deployment_summary.update_status("completed", "Deployment completed")
    logging.info("Updated status to completed")

    # Check if the summary file was created
    summary_file = f"deployment_summary_{project_id}.json"
    if os.path.exists(summary_file):
        with open(summary_file, 'r') as f:
            summary = json.load(f)
            logging.info(f"Summary file content: {json.dumps(summary, indent=2)}")
        return True
    else:
        logging.error(f"Summary file {summary_file} not found")
        return False

async def test_update_deployment_summary():
    """Test the update_deployment_summary function."""
    project_id = "test-project"
    api_url = "http://localhost:50508"

    if not using_deployment_status_module or update_deployment_summary is None:
        logging.error("update_deployment_summary function not available. Test failed.")
        return False

    logging.info("Testing update_deployment_summary function...")

    # Update the deployment status
    await update_deployment_summary(
        project_id=project_id,
        status="in_progress",
        message="Deployment in progress",
        api_url=api_url
    )
    logging.info("Updated deployment status")

    # Check if the summary file was created
    summary_file = f"deployment_summary_{project_id}.json"
    if os.path.exists(summary_file):
        with open(summary_file, 'r') as f:
            summary = json.load(f)
            logging.info(f"Summary file content: {json.dumps(summary, indent=2)}")
        return True
    else:
        logging.error(f"Summary file {summary_file} not found")
        return False

async def test_add_deployment_resource():
    """Test the add_deployment_resource function."""
    project_id = "test-project"
    api_url = "http://localhost:50508"

    if not using_deployment_status_module or add_deployment_resource is None:
        logging.error("add_deployment_resource function not available. Test failed.")
        return False

    logging.info("Testing add_deployment_resource function...")

    # Add a resource
    await add_deployment_resource(
        project_id=project_id,
        resource_name="test_resource",
        resource_value="test_value",
        api_url=api_url
    )
    logging.info("Added resource")

    # Check if the summary file was created
    summary_file = f"deployment_summary_{project_id}.json"
    if os.path.exists(summary_file):
        with open(summary_file, 'r') as f:
            summary = json.load(f)
            logging.info(f"Summary file content: {json.dumps(summary, indent=2)}")
        return True
    else:
        logging.error(f"Summary file {summary_file} not found")
        return False

async def test_add_deployment_error():
    """Test the add_deployment_error function."""
    project_id = "test-project"
    api_url = "http://localhost:50508"

    if not using_deployment_status_module or add_deployment_error is None:
        logging.error("add_deployment_error function not available. Test failed.")
        return False

    logging.info("Testing add_deployment_error function...")

    # Add an error
    await add_deployment_error(
        project_id=project_id,
        error="Test error message",
        api_url=api_url
    )
    logging.info("Added error")

    # Check if the summary file was created
    summary_file = f"deployment_summary_{project_id}.json"
    if os.path.exists(summary_file):
        with open(summary_file, 'r') as f:
            summary = json.load(f)
            logging.info(f"Summary file content: {json.dumps(summary, indent=2)}")
        return True
    else:
        logging.error(f"Summary file {summary_file} not found")
        return False

async def create_test_project():
    """Create a test project in the database."""
    project_id = f"test-project-{uuid.uuid4().hex[:8]}"
    project_name = f"Test Project {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    region_id = "westeurope"

    logging.info(f"Creating test project: {project_id} - {project_name}")

    # Create a project JSON file
    project_data = {
        "id": project_id,
        "name": project_name,
        "region": region_id,
        "type": "project",
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
        "deployment": {
            "status": "pending",
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "message": "Project created, deployment pending"
        }
    }

    # Save the project data to a file
    with open(f"{project_id}.json", "w") as f:
        json.dump(project_data, f, indent=2)

    logging.info(f"Project data saved to {project_id}.json")

    return project_id, project_name, region_id

async def deploy_test_project(project_id, project_name, region_id):
    """Deploy the test project."""
    api_url = "http://localhost:50508"

    logging.info(f"Starting deployment for project: {project_id}")

    # Update the deployment status to in_progress
    if using_deployment_status_module:
        await update_deployment_summary(
            project_id=project_id,
            status="in_progress",
            message="Starting deployment...",
            api_url=api_url
        )

    # Deploy the project resources
    try:
        await deploy_project_resources(
            project_id=project_id,
            project_name=project_name,
            region_id=region_id,
            api_url=api_url
        )
        logging.info(f"Deployment completed for project: {project_id}")
        return True
    except Exception as e:
        logging.error(f"Deployment failed: {e}")

        # Update the deployment status to failed
        if using_deployment_status_module:
            await update_deployment_summary(
                project_id=project_id,
                status="failed",
                message=f"Deployment failed: {e}",
                api_url=api_url
            )

            # Add the error
            await add_deployment_error(
                project_id=project_id,
                error=str(e),
                api_url=api_url
            )

        return False

async def check_deployment_status(project_id):
    """Check the deployment status."""
    # Check if the summary file was created
    summary_file = f"deployment_summary_{project_id}.json"
    if os.path.exists(summary_file):
        with open(summary_file, 'r') as f:
            summary = json.load(f)
            logging.info(f"Deployment summary: {json.dumps(summary, indent=2)}")
        return True
    else:
        logging.error(f"Summary file {summary_file} not found")
        return False

async def main():
    """Main function."""
    logging.info("Starting deployment tests...")

    # Choose which tests to run
    run_basic_tests = False
    run_real_deployment = True

    if run_basic_tests:
        # Test the DeploymentSummary class
        result1 = await test_deployment_summary()
        logging.info(f"DeploymentSummary test {'passed' if result1 else 'failed'}")

        # Test the update_deployment_summary function
        result2 = await test_update_deployment_summary()
        logging.info(f"update_deployment_summary test {'passed' if result2 else 'failed'}")

        # Test the add_deployment_resource function
        result3 = await test_add_deployment_resource()
        logging.info(f"add_deployment_resource test {'passed' if result3 else 'failed'}")

        # Test the add_deployment_error function
        result4 = await test_add_deployment_error()
        logging.info(f"add_deployment_error test {'passed' if result4 else 'failed'}")

        # Overall result for basic tests
        basic_tests_result = all([result1, result2, result3, result4])
        logging.info(f"Basic tests result: {'passed' if basic_tests_result else 'failed'}")

    if run_real_deployment:
        # Create a test project
        project_id, project_name, region_id = await create_test_project()

        # Deploy the test project
        deployment_success = await deploy_test_project(project_id, project_name, region_id)

        # Check the deployment status
        status_check = await check_deployment_status(project_id)

        # Print the results
        logging.info(f"Project ID: {project_id}")
        logging.info(f"Project Name: {project_name}")
        logging.info(f"Deployment Success: {deployment_success}")
        logging.info(f"Status Check: {status_check}")

        # Overall result for real deployment
        real_deployment_result = deployment_success and status_check
        logging.info(f"Real deployment test result: {'passed' if real_deployment_result else 'failed'}")

        return real_deployment_result

    return basic_tests_result if run_basic_tests else True

if __name__ == "__main__":
    asyncio.run(main())
